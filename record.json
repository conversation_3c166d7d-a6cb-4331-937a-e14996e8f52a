{"apifoxProject": "1.0.0", "$schema": {"app": "apifox", "type": "project", "version": "1.2.0"}, "info": {"name": "璞泰来园区系统", "description": "", "mockRule": {"rules": [], "enableSystemRule": true}}, "apiCollection": [{"name": "根目录", "id": 63480796, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "methodAndPath", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "SHARED", "moduleId": 5953032, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "visitor", "id": 64016924, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 5953032, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "管理后台 - 访客记录", "id": 64023392, "auth": {}, "securityScheme": {}, "parentId": 64016924, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": ""}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 5953032, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "创建访客记录", "api": {"id": "335518357", "method": "post", "path": "/visitor/record/create", "parameters": {"query": [], "path": [], "cookie": [], "header": [{"required": false, "description": "", "type": "string", "id": "56MnqtbhOh", "example": "{{Authorization}}", "enable": true, "name": "Authorization"}, {"required": false, "description": "", "type": "string", "id": "l9Xr8qeHah", "example": "{{tenant-id}}", "enable": true, "name": "tenant-id"}]}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "responses": [{"id": "744273307", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/191242264"}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n  \"code\": 0,\n  \"data\": 0,\n  \"msg\": \"\"\n}", "responseId": 744273307, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/192638278", "description": ""}, "mediaType": "", "examples": [{"name": "示例 1", "value": "{\n    \"applicationId\": 1024,\n    \"visitorName\": \"张三\",\n    \"visitorPhone\": \"***********\",\n    \"operationType\": 1,\n    \"operationTime\": \"1999-08-17 05:56:59\",\n    \"operatorId\": 1024,\n    \"operatorName\": \"李四\",\n    \"gateLocation\": \"东门\",\n    \"verificationMethod\": 1,\n    \"vehiclePlate\": \"苏A12345\",\n    \"vehiclePhoto\": \"https://example.com/vehicle.jpg\",\n    \"visitorPhoto\": \"https://example.com/visitor.jpg\",\n    \"abnormalInfo\": \"无异常\",\n    \"abnormalPhotos\": [\n        \"https://loremflickr.com/400/400?lock=4160171415250615\",\n        \"https://loremflickr.com/400/400?lock=2731189410779000\",\n        \"https://loremflickr.com/400/400?lock=4188634597941513\"\n    ],\n    \"temperature\": 36.5,\n    \"healthStatus\": 1,\n    \"securityCheckResult\": 1,\n    \"remarks\": \"正常通行\"\n}", "mediaType": "application/json"}], "oasExtensions": ""}, "description": "创建访客记录", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 0, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.744273307"], "visibility": "INHERITED", "moduleId": 5953032, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "获得访客记录分页", "api": {"id": "335518358", "method": "get", "path": "/visitor/record/page", "parameters": {"path": [], "query": [{"name": "pageNo", "required": true, "description": "页码，从 1 开始", "example": "1", "type": "integer", "schema": {"type": "integer", "minimum": 1, "default": 1, "examples": [1]}, "id": "k4pnVAno1M"}, {"name": "pageSize", "required": true, "description": "每页条数，最大值为 100", "example": "10", "type": "integer", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "examples": [10]}, "id": "fMcDENMEGO"}, {"name": "applicationId", "required": false, "description": "申请单ID", "example": "1024", "type": "integer", "schema": {"type": "integer", "format": "int64", "examples": [1024]}, "id": "FMSPxE5GpB"}, {"name": "applicationNo", "required": false, "description": "申请单号", "example": "VA20231201001", "type": "string", "schema": {"type": "string", "examples": ["VA20231201001"]}, "id": "ZcKIk33bPg"}, {"name": "visitorName", "required": false, "description": "访客姓名", "example": "张三", "type": "string", "schema": {"type": "string", "examples": ["张三"]}, "id": "U83ocBwTvu"}, {"name": "visitorPhone", "required": false, "description": "访客电话", "example": "***********", "type": "string", "schema": {"type": "string", "examples": ["***********"]}, "id": "DpqFRWKhYo"}, {"name": "operationType", "required": false, "description": "操作类型", "example": "1", "type": "integer", "schema": {"type": "integer", "examples": [1]}, "id": "ERvpD47AJw"}, {"name": "operatorId", "required": false, "description": "操作人ID", "example": "1024", "type": "integer", "schema": {"type": "integer", "format": "int64", "examples": [1024]}, "id": "j6ui4mcs1S"}, {"name": "operatorName", "required": false, "description": "操作人姓名", "example": "李四", "type": "string", "schema": {"type": "string", "examples": ["李四"]}, "id": "0X1obnzGWJ"}, {"name": "gateLocation", "required": false, "description": "门岗位置", "example": "东门", "type": "string", "schema": {"type": "string", "examples": ["东门"]}, "id": "0Tw9eTF0tP"}, {"name": "verificationMethod", "required": false, "description": "验证方式", "example": "1", "type": "integer", "schema": {"type": "integer", "examples": [1]}, "id": "hZxxGQrd4S"}, {"name": "vehiclePlate", "required": false, "description": "车牌号", "example": "苏A12345", "type": "string", "schema": {"type": "string", "examples": ["苏A12345"]}, "id": "wkJGBm37hl"}, {"name": "healthStatus", "required": false, "description": "健康状态", "example": "1", "type": "integer", "schema": {"type": "integer", "examples": [1]}, "id": "tXBgt3Bx5H"}, {"name": "securityCheckResult", "required": false, "description": "安检结果", "example": "1", "type": "integer", "schema": {"type": "integer", "examples": [1]}, "id": "106Kv62ZHZ"}, {"name": "operationTime", "required": false, "description": "操作时间", "type": "array", "schema": {"type": "array", "items": {"type": "string"}}, "id": "lS1DLXgliJ"}, {"name": "createTime", "required": false, "description": "创建时间", "type": "array", "schema": {"type": "array", "items": {"type": "string"}}, "id": "8n6bF7xUYo"}], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "744273308", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/192638281"}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n  \"code\": 0,\n  \"data\": {\n    \"list\": [\n      {\n        \"createTime\": \"\",\n        \"updateTime\": \"\",\n        \"creator\": \"\",\n        \"updater\": \"\",\n        \"deleted\": false,\n        \"tenantId\": 0,\n        \"id\": 0,\n        \"applicationId\": 0,\n        \"visitorName\": \"\",\n        \"visitorPhone\": \"\",\n        \"operationType\": 0,\n        \"operationTime\": \"\",\n        \"operatorId\": 0,\n        \"operatorName\": \"\",\n        \"gateLocation\": \"\",\n        \"verificationMethod\": 0,\n        \"vehiclePlate\": \"\",\n        \"vehiclePhoto\": \"\",\n        \"visitorPhoto\": \"\",\n        \"abnormalInfo\": \"\",\n        \"abnormalPhotos\": [\n          \"\"\n        ],\n        \"temperature\": 0.0,\n        \"healthStatus\": 0,\n        \"securityCheckResult\": 0,\n        \"itemCheckResult\": 0,\n        \"onTimeExit\": 0,\n        \"abnormalSituation\": \"\",\n        \"remarks\": \"\"\n      }\n    ],\n    \"total\": 0\n  },\n  \"msg\": \"\"\n}", "responseId": 744273308, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "获得访客记录分页", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 6, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {"enable": true}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5953032, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "获得访客记录", "api": {"id": "335518359", "method": "get", "path": "/visitor/record/get", "parameters": {"path": [], "query": [{"name": "id", "required": true, "description": "编号", "example": "1024", "type": "integer", "schema": {"type": "integer"}, "id": "hmnpoonae0"}], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "744273309", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/192638282"}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n  \"code\": 0,\n  \"data\": {\n    \"createTime\": \"\",\n    \"updateTime\": \"\",\n    \"creator\": \"\",\n    \"updater\": \"\",\n    \"deleted\": false,\n    \"tenantId\": 0,\n    \"id\": 0,\n    \"applicationId\": 0,\n    \"visitorName\": \"\",\n    \"visitorPhone\": \"\",\n    \"operationType\": 0,\n    \"operationTime\": \"\",\n    \"operatorId\": 0,\n    \"operatorName\": \"\",\n    \"gateLocation\": \"\",\n    \"verificationMethod\": 0,\n    \"vehiclePlate\": \"\",\n    \"vehiclePhoto\": \"\",\n    \"visitorPhoto\": \"\",\n    \"abnormalInfo\": \"\",\n    \"abnormalPhotos\": [\n      \"\"\n    ],\n    \"temperature\": 0.0,\n    \"healthStatus\": 0,\n    \"securityCheckResult\": 0,\n    \"itemCheckResult\": 0,\n    \"onTimeExit\": 0,\n    \"abnormalSituation\": \"\",\n    \"remarks\": \"\"\n  },\n  \"msg\": \"\"\n}", "responseId": 744273309, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "获得访客记录", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 12, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5953032, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "根据申请ID获得记录列表", "api": {"id": "335518360", "method": "get", "path": "/visitor/record/list-by-application", "parameters": {"path": [], "query": [{"name": "applicationId", "required": true, "description": "申请ID", "example": "1024", "type": "integer", "schema": {"type": "integer"}, "id": "FLSQGvqyWL"}], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "744273310", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/192638283"}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n  \"code\": 0,\n  \"data\": [\n    {\n      \"createTime\": \"\",\n      \"updateTime\": \"\",\n      \"creator\": \"\",\n      \"updater\": \"\",\n      \"deleted\": false,\n      \"tenantId\": 0,\n      \"id\": 0,\n      \"applicationId\": 0,\n      \"visitorName\": \"\",\n      \"visitorPhone\": \"\",\n      \"operationType\": 0,\n      \"operationTime\": \"\",\n      \"operatorId\": 0,\n      \"operatorName\": \"\",\n      \"gateLocation\": \"\",\n      \"verificationMethod\": 0,\n      \"vehiclePlate\": \"\",\n      \"vehiclePhoto\": \"\",\n      \"visitorPhoto\": \"\",\n      \"abnormalInfo\": \"\",\n      \"abnormalPhotos\": [\n        \"\"\n      ],\n      \"temperature\": 0.0,\n      \"healthStatus\": 0,\n      \"securityCheckResult\": 0,\n      \"itemCheckResult\": 0,\n      \"onTimeExit\": 0,\n      \"abnormalSituation\": \"\",\n      \"remarks\": \"\"\n    }\n  ],\n  \"msg\": \"\"\n}", "responseId": 744273310, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "根据申请ID获得记录列表", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 18, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5953032, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "二维码入园", "api": {"id": "335518361", "method": "post", "path": "/visitor/record/entry-by-qrcode", "parameters": {"path": [], "query": [{"name": "qrCode<PERSON>ontent", "required": true, "description": "", "type": "string", "schema": {"type": "string"}, "id": "Wzflw15rej"}, {"name": "gateLocation", "required": true, "description": "", "type": "string", "schema": {"type": "string"}, "id": "04kQXJjkyV"}, {"name": "operatorId", "required": true, "description": "", "type": "integer", "schema": {"type": "integer"}, "id": "6PeCMXINhq"}], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "744273311", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/191242264"}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n  \"code\": 0,\n  \"data\": 0,\n  \"msg\": \"\"\n}", "responseId": 744273311, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "二维码入园", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 24, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5953032, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "检查是否已入园", "api": {"id": "335518362", "method": "get", "path": "/visitor/record/check-entry", "parameters": {"path": [], "query": [{"name": "applicationId", "required": true, "description": "申请ID", "example": "1024", "type": "integer", "schema": {"type": "integer"}, "id": "9h40zJWi9t"}], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "744273312", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/191242266"}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n  \"code\": 0,\n  \"data\": false,\n  \"msg\": \"\"\n}", "responseId": 744273312, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "检查是否已入园", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 30, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5953032, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "检查是否已出园", "api": {"id": "335518363", "method": "get", "path": "/visitor/record/check-exit", "parameters": {"path": [], "query": [{"name": "applicationId", "required": true, "description": "申请ID", "example": "1024", "type": "integer", "schema": {"type": "integer"}, "id": "HH0s9LYx9B"}], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "744273313", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/191242266"}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\n  \"code\": 0,\n  \"data\": false,\n  \"msg\": \"\"\n}", "responseId": 744273313, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "检查是否已出园", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 36, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5953032, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}]}]}]}], "socketCollection": [], "docCollection": [], "webSocketCollection": [], "socketIOCollection": [], "responseCollection": [{"_databaseId": 7221438, "updatedAt": "2025-08-06T12:07:47.000Z", "name": "根目录", "type": "root", "children": [], "moduleId": 5953032, "parentId": 0, "id": 7221438, "ordering": [], "items": []}], "schemaCollection": [{"id": 15794086, "name": "根目录", "visibility": "SHARED", "moduleId": 5953032, "items": [{"name": "VisitorRecordCreateReqVO", "displayName": "", "id": "#/definitions/192638278", "description": "cn.iocoder.yudao.module.visitor.controller.admin.vo.record.VisitorRecordCreateReqVO", "schema": {"jsonSchema": {"type": "object", "properties": {"applicationId": {"type": "integer", "description": "申请单ID", "format": "int64", "examples": [1024]}, "visitorName": {"type": "string", "description": "访客姓名", "maxLength": 50, "examples": ["张三"], "x-apifox-mock": "@string(0,50)"}, "visitorPhone": {"type": "string", "description": "访客电话", "maxLength": 20, "examples": ["***********"], "x-apifox-mock": "@string(0,20)"}, "operationType": {"type": "integer", "description": "操作类型", "examples": [1]}, "operationTime": {"type": "string", "description": "操作时间", "x-apifox-mock": "@datetime"}, "operatorId": {"type": "integer", "description": "操作人ID", "format": "int64", "examples": [1024]}, "operatorName": {"type": "string", "description": "操作人姓名", "maxLength": 50, "examples": ["李四"], "x-apifox-mock": "@string(0,50)"}, "gateLocation": {"type": "string", "description": "门岗位置", "maxLength": 100, "examples": ["东门"], "x-apifox-mock": "@string(0,100)"}, "verificationMethod": {"type": "integer", "description": "验证方式", "examples": [1]}, "vehiclePlate": {"type": "string", "description": "车牌号", "maxLength": 20, "examples": ["苏A12345"], "x-apifox-mock": "@string(0,20)"}, "vehiclePhoto": {"type": "string", "description": "车辆照片URL", "examples": ["https://example.com/vehicle.jpg"]}, "visitorPhoto": {"type": "string", "description": "现场访客照片URL", "examples": ["https://example.com/visitor.jpg"]}, "abnormalInfo": {"type": "string", "description": "异常情况描述", "maxLength": 500, "examples": ["无异常"], "x-apifox-mock": "@string(0,500)"}, "abnormalPhotos": {"type": "array", "items": {"type": "string"}, "description": "异常照片URL数组"}, "temperature": {"type": "number", "description": "体温（摄氏度）", "minimum": 30, "maximum": 45, "examples": [36.5], "x-apifox-mock": "@float(30.0,45.0,2)"}, "healthStatus": {"type": "integer", "description": "健康状态", "examples": [1]}, "securityCheckResult": {"type": "integer", "description": "安检结果", "examples": [1]}, "remarks": {"type": "string", "description": "备注信息", "maxLength": 500, "examples": ["正常通行"], "x-apifox-mock": "@string(0,500)"}}, "x-apifox-orders": ["applicationId", "visitorName", "visitorPhone", "operationType", "operationTime", "operatorId", "operatorName", "gateLocation", "verificationMethod", "vehiclePlate", "vehiclePhoto", "visitorPhoto", "abnormalInfo", "<PERSON><PERSON><PERSON><PERSON>", "temperature", "healthStatus", "securityCheckResult", "remarks"], "required": ["applicationId", "visitorName", "operationType", "operationTime"]}}, "visibility": "INHERITED", "moduleId": 5953032}, {"name": "CommonResultLong", "displayName": "", "id": "#/definitions/191242264", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "data": {"type": "integer", "description": "返回数据", "format": "int64"}, "msg": {"type": "string", "description": "错误提示，用户可阅读"}}, "x-apifox-orders": ["code", "data", "msg"]}}, "visibility": "INHERITED", "moduleId": 5953032}, {"name": "CommonResultPageResultVisitorRecordDO", "displayName": "", "id": "#/definitions/192638281", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "data": {"$ref": "#/definitions/192638280", "description": "返回数据"}, "msg": {"type": "string", "description": "错误提示，用户可阅读"}}, "x-apifox-orders": ["code", "data", "msg"]}}, "visibility": "INHERITED", "moduleId": 5953032}, {"name": "PageResultVisitorRecordDO", "displayName": "", "id": "#/definitions/192638280", "description": "返回数据", "schema": {"jsonSchema": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/192638279", "description": "访客进出记录 DO"}, "description": "数据"}, "total": {"type": "integer", "description": "总量", "format": "int64"}}, "x-apifox-orders": ["list", "total"]}}, "visibility": "INHERITED", "moduleId": 5953032}, {"name": "VisitorRecordDO", "displayName": "", "id": "#/definitions/192638279", "description": "访客进出记录 DO", "schema": {"jsonSchema": {"type": "object", "properties": {"createTime": {"type": "string", "description": "创建时间", "x-apifox-mock": "@datetime"}, "updateTime": {"type": "string", "description": "最后更新时间", "x-apifox-mock": "@datetime"}, "creator": {"type": "string", "description": "创建者，目前使用 SysUser 的 id 编号\n\n使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。"}, "updater": {"type": "string", "description": "更新者，目前使用 SysUser 的 id 编号\n\n使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。"}, "deleted": {"type": "boolean", "description": "是否删除"}, "tenantId": {"type": "integer", "description": "多租户编号", "format": "int64"}, "id": {"type": "integer", "description": "主键ID", "format": "int64"}, "applicationId": {"type": "integer", "description": "申请单ID", "format": "int64"}, "visitorName": {"type": "string", "description": "访客姓名"}, "visitorPhone": {"type": "string", "description": "访客电话"}, "operationType": {"type": "integer", "description": "操作类型：1-入园 2-出园\n\n枚举{@link cn.iocoder.yudao.module.visitor.enums.RecordTypeEnum}"}, "operationTime": {"type": "string", "description": "操作时间", "x-apifox-mock": "@datetime"}, "operatorId": {"type": "integer", "description": "操作人ID", "format": "int64"}, "operatorName": {"type": "string", "description": "操作人姓名"}, "gateLocation": {"type": "string", "description": "门岗位置"}, "verificationMethod": {"type": "integer", "description": "验证方式：1-二维码 2-手动 3-人脸识别"}, "vehiclePlate": {"type": "string", "description": "车牌号"}, "vehiclePhoto": {"type": "string", "description": "车辆照片URL"}, "visitorPhoto": {"type": "string", "description": "现场访客照片URL"}, "abnormalInfo": {"type": "string", "description": "异常情况描述"}, "abnormalPhotos": {"type": "array", "items": {"type": "string"}, "description": "异常照片URL数组"}, "temperature": {"type": "number", "description": "体温（摄氏度）"}, "healthStatus": {"type": "integer", "description": "健康状态：1-正常 2-异常"}, "securityCheckResult": {"type": "integer", "description": "安检结果：1-通过 2-未通过"}, "itemCheckResult": {"type": "integer", "description": "物品检查结果：1-正常 2-异常"}, "onTimeExit": {"type": "integer", "description": "是否按时离园：1-是 2-否"}, "abnormalSituation": {"type": "string", "description": "异常情况描述"}, "remarks": {"type": "string", "description": "备注信息"}}, "x-apifox-orders": ["createTime", "updateTime", "creator", "updater", "deleted", "tenantId", "id", "applicationId", "visitorName", "visitorPhone", "operationType", "operationTime", "operatorId", "operatorName", "gateLocation", "verificationMethod", "vehiclePlate", "vehiclePhoto", "visitorPhoto", "abnormalInfo", "<PERSON><PERSON><PERSON><PERSON>", "temperature", "healthStatus", "securityCheckResult", "itemCheckResult", "onTimeExit", "abnormalSituation", "remarks"]}}, "visibility": "INHERITED", "moduleId": 5953032}, {"name": "CommonResultVisitorRecordDO", "displayName": "", "id": "#/definitions/192638282", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "data": {"$ref": "#/definitions/192638279", "description": "返回数据"}, "msg": {"type": "string", "description": "错误提示，用户可阅读"}}, "x-apifox-orders": ["code", "data", "msg"]}}, "visibility": "INHERITED", "moduleId": 5953032}, {"name": "CommonResultBoolean", "displayName": "", "id": "#/definitions/191242266", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "data": {"type": "boolean", "description": "返回数据"}, "msg": {"type": "string", "description": "错误提示，用户可阅读"}}, "x-apifox-orders": ["code", "data", "msg"]}}, "visibility": "INHERITED", "moduleId": 5953032}, {"name": "CommonResultListVisitorRecordDO", "displayName": "", "id": "#/definitions/192638283", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "data": {"type": "array", "items": {"$ref": "#/definitions/192638279", "description": "访客进出记录 DO"}, "description": "返回数据"}, "msg": {"type": "string", "description": "错误提示，用户可阅读"}}, "x-apifox-orders": ["code", "data", "msg"]}}, "visibility": "INHERITED", "moduleId": 5953032}]}], "securitySchemeCollection": [], "requestCollection": [{"name": "根目录", "children": [], "ordering": ["requestFolder.7347699"], "items": []}], "environments": [], "commonScripts": [], "globalVariables": [], "moduleVariables": [{"id": "5953032", "variables": []}], "commonParameters": null, "projectSetting": {"id": "6940413", "auth": {}, "securityScheme": {}, "gateway": [], "language": "zh-CN", "apiStatuses": ["developing", "testing", "released", "deprecated"], "mockSettings": {}, "preProcessors": [], "postProcessors": [], "advancedSettings": {"enableJsonc": false, "enableBigint": false, "responseValidate": true, "enableTestScenarioSetting": false, "enableYAPICompatScript": false, "isDefaultUrlEncoding": 2, "publishedDocUrlRules": {"defaultRule": "RESOURCE_KEY_ONLY", "resourceKeyStandard": "NEW"}}, "initialDisabledMockIds": [], "servers": [{"id": "default", "name": "默认服务"}], "cloudMock": {"security": "free", "enable": false, "tokenKey": "apifoxToken"}}, "customFunctions": [], "projectTestCaseCategories": [{"id": 162586, "name": "正向"}, {"id": 162587, "name": "负向"}, {"id": 162588, "name": "边界值"}, {"id": 162589, "name": "安全性"}, {"id": 162590, "name": "其他"}], "projectAssociations": []}