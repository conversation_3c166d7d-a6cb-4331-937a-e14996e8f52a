import request from '@/config/axios'

// 访客申请 VO
export interface VisitorApplicationVO {
  id?: number
  applicationNo?: string
  visitorName: string
  visitorPhone: string
  visitorIdCard?: string
  companyName: string
  visitorType: number // 1-普通访客 2-政府访客 3-承包商
  contactPerson: string
  contactPhone?: string
  contactDeptId?: number
  contactDeptName?: string
  visitPurpose: string
  visitStartTime: string
  visitEndTime: string
  visitArea: string
  hasVehicle: number // 0-否 1-是
  vehiclePlate?: string
  vehicleType?: string
  accompanyCount?: number
  accompanyPersons?: string
  status: number // 1-待确认 2-已审批 3-已驳回 4-已入园 5-已出园 6-已取消
  approver?: string
  approveTime?: string
  approveRemark?: string
  qrcodeUrl?: string
  createTime?: string
  updateTime?: string
  remark?: string
}

// 访客申请创建请求 VO
export interface VisitorApplicationCreateReqVO {
  visitorName: string
  visitorPhone: string
  visitorIdCard?: string
  companyName: string
  visitorType: number
  contactPerson: string
  contactPhone?: string
  contactDeptId?: number
  visitPurpose: string
  visitStartTime: string
  visitEndTime: string
  visitArea: string
  hasVehicle: number
  vehiclePlate?: string
  vehicleType?: string
  accompanyCount?: number
  accompanyPersons?: string
  remark?: string
}

// 访客申请更新请求 VO
export interface VisitorApplicationUpdateReqVO extends VisitorApplicationCreateReqVO {
  id: number
}

// 访客申请分页查询参数
export interface VisitorApplicationPageReqVO {
  pageNo: number
  pageSize: number
  applicationNo?: string
  visitorName?: string
  visitorPhone?: string
  companyName?: string
  visitorType?: number
  status?: number
  contactPerson?: string
  contactDeptId?: number
  visitArea?: string
  createTime?: string[]
  visitStartTime?: string[]
  hasVehicle?: number
  vehiclePlate?: string
}

// 审批请求 VO
export interface VisitorApplicationApproveReqVO {
  id: number
  approveResult: number // 1-通过 2-驳回
  approveOpinion?: string
}

// 访客申请 API
export const getApplicationPage = async (params: VisitorApplicationPageReqVO) => {
  return await request.get({ url: '/visitor/application/page', params })
}

export const getApplication = async (id: number) => {
  return await request.get({ url: '/visitor/application/get', params: { id } })
}

export const approveApplication = async (data: VisitorApplicationApproveReqVO) => {
  return await request.post({ url: '/visitor/application/approve', data })
}
