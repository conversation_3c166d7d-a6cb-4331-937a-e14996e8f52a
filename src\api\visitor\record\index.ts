import request from '@/config/axios'

// 访客记录 API
export interface VisitorRecordVO {
  id?: number
  createTime?: string
  updateTime?: string
  creator?: string
  updater?: string
  deleted?: boolean
  tenantId?: number
  applicationId?: number
  visitorName?: string
  visitorPhone?: string
  operationType?: number
  operationTime?: string
  operatorId?: number
  operatorName?: string
  gateLocation?: string
  verificationMethod?: number
  vehiclePlate?: string
  vehiclePhoto?: string
  visitorPhoto?: string
  abnormalInfo?: string
  abnormalPhotos?: string[]
  temperature?: number
  healthStatus?: number
  securityCheckResult?: number
  itemCheckResult?: number
  onTimeExit?: number
  abnormalSituation?: string
  remarks?: string
}

export interface VisitorRecordCreateReqVO {
  applicationId: number
  visitorName: string
  visitorPhone?: string
  operationType: number
  operationTime: string
  operatorId?: number
  operatorName?: string
  gateLocation?: string
  verificationMethod?: number
  vehiclePlate?: string
  vehiclePhoto?: string
  visitorPhoto?: string
  abnormalInfo?: string
  abnormalPhotos?: string[]
  temperature?: number
  healthStatus?: number
  securityCheckResult?: number
  remarks?: string
}

export interface VisitorRecordPageReqVO extends PageParam {
  applicationId?: number
  applicationNo?: string
  visitorName?: string
  visitorPhone?: string
  operationType?: number
  operatorId?: number
  operatorName?: string
  gateLocation?: string
  verificationMethod?: number
  vehiclePlate?: string
  healthStatus?: number
  securityCheckResult?: number
  operationTime?: string[]
  createTime?: string[]
}

// 创建访客记录
export const createVisitorRecord = (data: VisitorRecordCreateReqVO) => {
  return request.post({ url: `/visitor/record/create`, data })
}

// 获得访客记录分页
export const getVisitorRecordPage = (params: VisitorRecordPageReqVO) => {
  return request.get({ url: `/visitor/record/page`, params })
}

// 获得访客记录
export const getVisitorRecord = (id: number) => {
  return request.get({ url: `/visitor/record/get?id=` + id })
}

// 根据申请ID获得记录列表
export const getVisitorRecordListByApplication = (applicationId: number) => {
  return request.get({ url: `/visitor/record/list-by-application?applicationId=` +
applicationId })
}

// 二维码入园
export const entryByQrcode = (qrCodeContent: string, gateLocation: string, operatorId: 
number) => {
  return request.post({
    url: `/visitor/record/entry-by-qrcode?qrCodeContent=${qrCodeContent}&gateLocation=${gateLocation}&operatorId=${operatorId}`
  })
}

// 检查是否已入园
export const checkEntry = (applicationId: number) => {
  return request.get({ url: `/visitor/record/check-entry?applicationId=` + applicationId })
}

// 检查是否已出园
export const checkExit = (applicationId: number) => {
  return request.get({ url: `/visitor/record/check-exit?applicationId=` + applicationId })
}