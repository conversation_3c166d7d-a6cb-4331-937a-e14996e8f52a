import { Layout } from '@/utils/routerHelper'

const { t } = useI18n()

const devVisitorRoutes: AppRouteRecordRaw[] = [
  {
    path: '/dev-visitor',
    component: Layout,
    name: 'DevVisitor',
    meta: {
      title: t('visitor.route.root'),
      icon: 'ep:histogram'
    },
    children: [
      {
        path: 'normal',
        component: () => import('@/views/visitor/apply/index.vue'),
        name: 'VisitorNormal',
        meta: {
          title: t('visitor.route.normal'),
          icon: 'ep:user'
        }
      }
    ]
  }
]

export default devVisitorRoutes
