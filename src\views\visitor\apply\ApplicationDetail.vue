<template>
  <Dialog v-model="dialogVisible" title="访客申请详情" width="800px">
    <el-descriptions v-if="formData" :column="2" border>
      <el-descriptions-item label="申请单号">
        {{ formData.applicationNo }}
      </el-descriptions-item>
      <el-descriptions-item label="申请状态">
        <dict-tag :type="DICT_TYPE.VISITOR_APPLICATION_STATUS" :value="formData.status" />
      </el-descriptions-item>
      <el-descriptions-item label="访客姓名">
        {{ formData.visitorName }}
      </el-descriptions-item>
      <el-descriptions-item label="访客电话">
        {{ formData.visitorPhone }}
      </el-descriptions-item>
      <el-descriptions-item label="身份证号">
        {{ formData.visitorIdCard }}
      </el-descriptions-item>
      <el-descriptions-item label="来访单位">
        {{ formData.companyName }}
      </el-descriptions-item>
      <el-descriptions-item label="访客类型">
        <dict-tag :type="DICT_TYPE.VISITOR_TYPE" :value="formData.visitorType" />
      </el-descriptions-item>
      <el-descriptions-item label="厂内联系人">
        {{ formData.contactPerson }}
      </el-descriptions-item>
      <el-descriptions-item label="联系人电话">
        {{ formData.contactPhone }}
      </el-descriptions-item>
      <el-descriptions-item label="联系人部门">
        {{ formData.contactDeptName }}
      </el-descriptions-item>
      <el-descriptions-item label="到访厂区">
        {{ formData.visitArea }}
      </el-descriptions-item>
      <el-descriptions-item label="来访事由">
        {{ formData.visitPurpose }}
      </el-descriptions-item>
      <el-descriptions-item label="预计到访时间">
        {{ formatDateTime(formData.visitStartTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="预计离开时间">
        {{ formatDateTime(formData.visitEndTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="是否驾车">
        <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="formData.hasVehicle" />
      </el-descriptions-item>
      <el-descriptions-item label="车牌号" v-if="formData.hasVehicle === 1">
        {{ formData.vehiclePlate }}
      </el-descriptions-item>
      <el-descriptions-item label="车辆类型" v-if="formData.hasVehicle === 1">
        {{ formData.vehicleType }}
      </el-descriptions-item>
      <el-descriptions-item label="随行人数">
        {{ formData.accompanyCount || 0 }}人
      </el-descriptions-item>
      <el-descriptions-item label="随行人员" v-if="formData.accompanyPersons">
        {{ formData.accompanyPersons }}
      </el-descriptions-item>
      <el-descriptions-item label="申请时间">
        {{ formatDateTime(formData.createTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="审批人" v-if="formData.approver">
        {{ formData.approver }}
      </el-descriptions-item>
      <el-descriptions-item label="审批时间" v-if="formData.approveTime">
        {{ formatDateTime(formData.approveTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="审批意见" v-if="formData.approveRemark" span="2">
        {{ formData.approveRemark }}
      </el-descriptions-item>
      <el-descriptions-item label="备注" v-if="formData.remark" span="2">
        {{ formData.remark }}
      </el-descriptions-item>
    </el-descriptions>
  </Dialog>
</template>

<script lang="ts" setup>
import { DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import * as VisitorApplicationApi from '@/api/visitor/application'

defineOptions({ name: 'ApplicationDetail' })

const dialogVisible = ref(false) // 弹窗的是否展示
const formData = ref<any>() // 表单数据

/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true
  try {
    formData.value = await VisitorApplicationApi.getApplication(id)
  } catch (error) {
    console.error('获取访客申请详情失败:', error)
  }
}

/** 格式化日期时间 */
const formatDateTime = (dateTime: string) => {
  return dateTime ? formatDate(dateTime, 'YYYY-MM-DD HH:mm:ss') : '-'
}

defineExpose({ open })
</script>
