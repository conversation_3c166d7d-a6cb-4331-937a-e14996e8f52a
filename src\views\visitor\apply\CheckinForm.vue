<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="600px">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="访客姓名">
          {{ applicationData.visitorName }}
        </el-descriptions-item>
        <el-descriptions-item label="来访单位">
          {{ applicationData.companyName }}
        </el-descriptions-item>
        <el-descriptions-item label="厂内联系人">
          {{ applicationData.contactPerson }}
        </el-descriptions-item>
      </el-descriptions>

      <el-divider />

      <el-form-item label="操作时间" prop="operationTime">
        <el-date-picker
          v-model="formData.operationTime"
          type="datetime"
          placeholder="选择操作时间"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>

      <el-form-item label="闸机位置" prop="gateLocation">
        <el-input v-model="formData.gateLocation" placeholder="请输入闸机位置" />
      </el-form-item>

      <el-form-item label="验证方式" prop="verificationMethod">
        <el-select v-model="formData.verificationMethod" placeholder="请选择验证方式">
          <el-option label="二维码" :value="1" />
          <el-option label="身份证" :value="2" />
          <el-option label="人脸识别" :value="3" />
          <el-option label="手动验证" :value="4" />
        </el-select>
      </el-form-item>

      <el-form-item label="体温" prop="temperature">
        <el-input-number
          v-model="formData.temperature"
          :precision="1"
          :min="35"
          :max="42"
          placeholder="请输入体温"
        />
        <span style="margin-left: 8px">°C</span>
      </el-form-item>

      <el-form-item label="健康状态" prop="healthStatus">
        <el-radio-group v-model="formData.healthStatus">
          <el-radio :label="1">正常</el-radio>
          <el-radio :label="2">异常</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="安检结果" prop="securityCheckResult">
        <el-radio-group v-model="formData.securityCheckResult">
          <el-radio :label="1">通过</el-radio>
          <el-radio :label="2">未通过</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="备注" prop="remarks">
        <el-input
          v-model="formData.remarks"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :loading="formLoading" type="primary" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import * as VisitorRecordApi from '@/api/visitor/record'
import { formatDate } from '@/utils/formatTime'

defineOptions({ name: 'CheckinForm' })

const { t } = useI18n()
const message = useMessage()

const dialogVisible = ref(false)
const dialogTitle = ref('')
const formLoading = ref(false)
const operationType = ref('')
const applicationData = ref<any>({})
const formData = ref({
  applicationId: undefined,
  visitorName: '',
  visitorPhone: '',
  operationType: 1, // 1-入园 2-出园
  operationTime: '',
  operatorId: undefined,
  operatorName: '',
  gateLocation: '',
  verificationMethod: 1,
  vehiclePlate: '',
  temperature: undefined,
  healthStatus: 1,
  securityCheckResult: 1,
  remarks: ''
})

const formRules = reactive({
  operationTime: [{ required: true, message: '操作时间不能为空', trigger: 'change' }],
  gateLocation: [{ required: true, message: '闸机位置不能为空', trigger: 'blur' }],
  verificationMethod: [{ required: true, message: '验证方式不能为空', trigger: 'change' }]
})

const formRef = ref()

/** 打开弹窗 */
const open = (row: any, type: string) => {
  dialogVisible.value = true
  operationType.value = type
  dialogTitle.value = type === 'checkin' ? '入园签到' : '出园签到'
  applicationData.value = row

  formData.value = {
    applicationId: row.id,
    visitorName: row.visitorName,
    visitorPhone: row.visitorPhone,
    operationType: type === 'checkin' ? 1 : 2,
    operationTime: formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss'),
    operatorId: undefined,
    operatorName: '',
    gateLocation: '',
    verificationMethod: 1,
    vehiclePlate: row.vehiclePlate || '',
    temperature: undefined,
    healthStatus: 1,
    securityCheckResult: 1,
    remarks: ''
  }
}

/** 提交表单 */
const emit = defineEmits(['success'])
const submitForm = async () => {
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return

  formLoading.value = true
  try {
    if (operationType.value === 'checkin') {
      await VisitorRecordApi.manualCheckin(formData.value)
      message.success('入园签到成功')
    } else {
      await VisitorRecordApi.manualCheckout(formData.value)
      message.success('出园签到成功')
    }
    dialogVisible.value = false
    emit('success')
  } finally {
    formLoading.value = false
  }
}

defineExpose({ open })
</script>
