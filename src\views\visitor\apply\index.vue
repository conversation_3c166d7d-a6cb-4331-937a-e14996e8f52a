<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="100px"
    >
      <el-form-item label="申请单号" prop="applicationNo">
        <el-input
          v-model="queryParams.applicationNo"
          class="!w-240px"
          clearable
          :placeholder="$t('visitor.application.placeholder.inputApplicationNo')"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="访客姓名" prop="visitorName">
        <el-input
          v-model="queryParams.visitorName"
          class="!w-240px"
          clearable
          placeholder="请输入访客姓名"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="访客电话" prop="visitorPhone">
        <el-input
          v-model="queryParams.visitorPhone"
          class="!w-240px"
          clearable
          placeholder="请输入访客电话"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="访客类型" prop="visitorType">
        <el-select
          v-model="queryParams.visitorType"
          class="!w-240px"
          clearable
          placeholder="请选择访客类型"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.VISITOR_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请状态" prop="status">
        <el-select
          v-model="queryParams.status"
          class="!w-240px"
          clearable
          placeholder="请选择申请状态"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.VISITOR_APPLICATION_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          type="datetimerange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item label="到访时间" prop="visitStartTime">
        <el-date-picker
          v-model="queryParams.visitStartTime"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          type="datetimerange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item label="车牌号" prop="vehiclePlate">
        <el-input
          v-model="queryParams.vehiclePlate"
          class="!w-240px"
          clearable
          placeholder="请输入车牌号"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否住宿" prop="needAccommodation">
        <el-select
          v-model="queryParams.needAccommodation"
          class="!w-240px"
          clearable
          placeholder="请选择是否住宿"
        >
          <el-option
            v-for="dict in getYesNoDictOptions()"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否就餐" prop="needDining">
        <el-select
          v-model="queryParams.needDining"
          class="!w-240px"
          clearable
          placeholder="请选择是否就餐"
        >
          <el-option
            v-for="dict in getYesNoDictOptions()"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <!-- TODO: navigate to create -->
        <el-button v-hasPermi="['visitor:application:create']" plain type="primary">
          <Icon class="mr-5px" icon="ep:plus" />
          新增
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" stripe>
      <el-table-column label="申请单号" prop="applicationNo" min-width="140" />
      <el-table-column label="访客姓名" prop="visitorName" min-width="100" sortable />
      <el-table-column label="联系方式" prop="visitorPhone" min-width="120" />
      <el-table-column label="访客类型" prop="visitorType" min-width="100" sortable>
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.VISITOR_TYPE" :value="scope.row.visitorType" />
        </template>
      </el-table-column>
      <el-table-column label="车牌号" prop="vehiclePlate" min-width="120" />
      <el-table-column label="是否住宿" prop="needAccommodation" min-width="100">
        <template #default="scope">
          <el-text :type="scope.row.needAccommodation === 1 ? 'success' : 'danger'">
            <Icon :icon="scope.row.needAccommodation === 1 ? 'ep:check' : 'ep:close'" />
          </el-text>
        </template>
      </el-table-column>
      <el-table-column label="是否就餐" prop="needDining" min-width="100">
        <template #default="scope">
          <el-text :type="scope.row.needDining === 1 ? 'success' : 'danger'">
            <Icon :icon="scope.row.needDining === 1 ? 'ep:check' : 'ep:close'" />
          </el-text>
        </template>
      </el-table-column>
      <el-table-column
        label="到访时间"
        prop="visitStartTime"
        min-width="140"
        sortable
        :formatter="dateFormatter"
      />
      <el-table-column label="申请状态" prop="status" min-width="100" sortable>
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.VISITOR_APPLICATION_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        prop="createTime"
        min-width="140"
        sortable
        :formatter="dateFormatter"
      />
      <el-table-column label="操作" width="240" fixed="right">
        <template #default="scope">
          <div class="flex items-center gap-2">
            <el-button
              v-hasPermi="['visitor:application:query']"
              link
              type="primary"
              @click="openDetail(scope.row.id)"
            >
              详情
            </el-button>
            <el-button
              v-if="scope.row.status === 2"
              v-hasPermi="['visitor:application:query']"
              link
              type="primary"
              @click="handleCheckin(scope.row)"
            >
              入园签到
            </el-button>
            <el-button
              v-if="scope.row.status === 4"
              v-hasPermi="['visitor:application:query']"
              link
              type="primary"
              @click="handleCheckout(scope.row)"
            >
              出园签到
            </el-button>
            <el-button
              v-if="[1, 2].includes(scope.row.status)"
              v-hasPermi="['visitor:application:approve']"
              link
              type="primary"
              @click="openApprove(scope.row)"
            >
              审批
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 申请详情对话框 -->
  <ApplicationDetail ref="detailRef" />
  <!-- 签到对话框 -->
  <CheckinForm ref="checkinRef" @success="getList" />
</template>

<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions, getYesNoDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as VisitorApplicationApi from '@/api/visitor/application'
import ApplicationDetail from './ApplicationDetail.vue'
import CheckinForm from './CheckinForm.vue'

defineOptions({ name: 'VisitorNormal' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref<any[]>([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  applicationNo: undefined,
  visitorName: undefined,
  visitorPhone: undefined,
  companyName: undefined,
  visitorType: undefined,
  status: undefined,
  contactPerson: undefined,
  contactDeptId: undefined,
  visitArea: undefined,
  createTime: [],
  visitStartTime: [],
  hasVehicle: undefined,
  vehiclePlate: undefined,
  needAccommodation: undefined,
  needDining: undefined
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await VisitorApplicationApi.getApplicationPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 详情操作 */
const detailRef = ref()
const openDetail = (id: number) => {
  detailRef.value.open(id)
}

/** 审批操作 */
const approvalRef = ref()
const openApprove = (row: any) => {}

/** 签到操作 */
const checkinRef = ref()
const handleCheckin = (row: any) => {
  checkinRef.value.open(row, 'checkin')
}

const handleCheckout = (row: any) => {
  checkinRef.value.open(row, 'checkout')
}

/** 取消申请操作 */
const handleCancel = async (id: number) => {
  try {
    await message.confirm('是否确认取消该申请？')
    await VisitorApplicationApi.cancelApplication(id)
    message.success(t('common.delSuccess'))
    await getList()
  } catch {}
}

/** 删除操作 */
const handleDelete = async (id: number) => {
  try {
    await message.confirm('是否确认删除该申请记录？')
    await VisitorApplicationApi.deleteApplication(id)
    message.success(t('common.delSuccess'))
    await getList()
  } catch {}
}

/** 格式化手机号 */
const formatPhone = (phone: string) => {
  if (!phone || phone.length !== 11) return phone
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/** 初始化 **/
onMounted(async () => {
  await getList()
})
</script>
