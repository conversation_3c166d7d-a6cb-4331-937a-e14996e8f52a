<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item :label="t('visitor.record.applicationId')" prop="applicationId">
        <el-input
          v-model="queryParams.applicationId"
          :placeholder="t('visitor.record.applicationIdPlaceholder')"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('visitor.record.applicationNo')" prop="applicationNo">
        <el-input
          v-model="queryParams.applicationNo"
          :placeholder="t('visitor.record.applicationNoPlaceholder')"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('visitor.record.visitorName')" prop="visitorName">
        <el-input
          v-model="queryParams.visitorName"
          :placeholder="t('visitor.record.visitorNamePlaceholder')"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('visitor.record.visitorPhone')" prop="visitorPhone">
        <el-input
          v-model="queryParams.visitorPhone"
          :placeholder="t('visitor.record.visitorPhonePlaceholder')"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('visitor.record.operationType')" prop="operationType">
        <el-select
          v-model="queryParams.operationType"
          :placeholder="t('visitor.record.operationTypePlaceholder')"
          clearable
          class="!w-240px"
        >
          <el-option :label="t('visitor.record.entry')" :value="1" />
          <el-option :label="t('visitor.record.exit')" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('visitor.record.operatorName')" prop="operatorName">
        <el-input
          v-model="queryParams.operatorName"
          :placeholder="t('visitor.record.operatorNamePlaceholder')"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('visitor.record.gateLocation')" prop="gateLocation">
        <el-input
          v-model="queryParams.gateLocation"
          :placeholder="t('visitor.record.gateLocationPlaceholder')"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('visitor.record.verificationMethod')" prop="verificationMethod">
        <el-select
          v-model="queryParams.verificationMethod"
          :placeholder="t('visitor.record.verificationMethodPlaceholder')"
          clearable
          class="!w-240px"
        >
          <el-option :label="t('visitor.record.qrcode')" :value="1" />
          <el-option :label="t('visitor.record.manual')" :value="2" />
          <el-option :label="t('visitor.record.faceRecognition')" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('visitor.record.vehiclePlate')" prop="vehiclePlate">
        <el-input
          v-model="queryParams.vehiclePlate"
          :placeholder="t('visitor.record.vehiclePlatePlaceholder')"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('visitor.record.healthStatus')" prop="healthStatus">
        <el-select
          v-model="queryParams.healthStatus"
          :placeholder="t('visitor.record.healthStatusPlaceholder')"
          clearable
          class="!w-240px"
        >
          <el-option :label="t('visitor.record.normal')" :value="1" />
          <el-option :label="t('visitor.record.abnormal')" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('visitor.record.securityCheckResult')" prop="securityCheckResult">
        <el-select
          v-model="queryParams.securityCheckResult"
          :placeholder="t('visitor.record.securityCheckResultPlaceholder')"
          clearable
          class="!w-240px"
        >
          <el-option :label="t('visitor.record.passed')" :value="1" />
          <el-option :label="t('visitor.record.notPassed')" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('visitor.record.operationTime')" prop="operationTime">
        <el-date-picker
          v-model="queryParams.operationTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetimerange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('visitor.record.createTime')" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetimerange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />
          {{ t('common.search') }}
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" />
          {{ t('common.reset') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column :label="t('visitor.record.id')" align="center" prop="id" />
      <el-table-column
        :label="t('visitor.record.applicationId')"
        align="center"
        prop="applicationId"
      />
      <el-table-column :label="t('visitor.record.visitorName')" align="center" prop="visitorName" />
      <el-table-column
        :label="t('visitor.record.visitorPhone')"
        align="center"
        prop="visitorPhone"
      />
      <el-table-column
        :label="t('visitor.record.operationType')"
        align="center"
        prop="operationType"
      >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.VISITOR_OPERATION_TYPE" :value="scope.row.operationType" />
        </template>
      </el-table-column>
      <el-table-column
        :label="t('visitor.record.operationTime')"
        align="center"
        prop="operationTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        :label="t('visitor.record.operatorName')"
        align="center"
        prop="operatorName"
      />
      <el-table-column
        :label="t('visitor.record.gateLocation')"
        align="center"
        prop="gateLocation"
      />
      <el-table-column
        :label="t('visitor.record.verificationMethod')"
        align="center"
        prop="verificationMethod"
      >
        <template #default="scope">
          <dict-tag
            :type="DICT_TYPE.VISITOR_VERIFICATION_METHOD"
            :value="scope.row.verificationMethod"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="t('visitor.record.vehiclePlate')"
        align="center"
        prop="vehiclePlate"
      />
      <el-table-column :label="t('visitor.record.temperature')" align="center" prop="temperature">
        <template #default="scope">
          {{ scope.row.temperature ? scope.row.temperature + '°C' : '-' }}
        </template>
      </el-table-column>
      <el-table-column :label="t('visitor.record.healthStatus')" align="center" prop="healthStatus">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.VISITOR_HEALTH_STATUS" :value="scope.row.healthStatus" />
        </template>
      </el-table-column>
      <el-table-column
        :label="t('visitor.record.securityCheckResult')"
        align="center"
        prop="securityCheckResult"
      >
        <template #default="scope">
          <dict-tag
            :type="DICT_TYPE.VISITOR_SECURITY_CHECK_RESULT"
            :value="scope.row.securityCheckResult"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="t('visitor.record.createTime')"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column :label="t('common.action')" align="center" width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openDetail(scope.row)"
            v-hasPermi="['visitor:record:query']"
          >
            {{ t('common.detail') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 详情对话框 -->
  <el-dialog
    :title="t('visitor.record.detail')"
    v-model="detailVisible"
    width="800px"
    append-to-body
  >
    <el-form :model="detailData" label-width="120px" v-if="detailData">
      <el-row>
        <el-col :span="12">
          <el-form-item :label="t('visitor.record.applicationId')">
            <span>{{ detailData.applicationId }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('visitor.record.visitorName')">
            <span>{{ detailData.visitorName }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item :label="t('visitor.record.visitorPhone')">
            <span>{{ detailData.visitorPhone }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('visitor.record.operationType')">
            <dict-tag :type="DICT_TYPE.VISITOR_OPERATION_TYPE" :value="detailData.operationType" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item :label="t('visitor.record.operationTime')">
            <span>{{ formatDate(detailData.operationTime) }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('visitor.record.operatorName')">
            <span>{{ detailData.operatorName }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item :label="t('visitor.record.gateLocation')">
            <span>{{ detailData.gateLocation }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('visitor.record.verificationMethod')">
            <dict-tag
              :type="DICT_TYPE.VISITOR_VERIFICATION_METHOD"
              :value="detailData.verificationMethod"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="detailData.vehiclePlate">
        <el-col :span="12">
          <el-form-item :label="t('visitor.record.vehiclePlate')">
            <span>{{ detailData.vehiclePlate }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('visitor.record.temperature')" v-if="detailData.temperature">
            <span>{{ detailData.temperature }}°C</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item :label="t('visitor.record.healthStatus')">
            <dict-tag :type="DICT_TYPE.VISITOR_HEALTH_STATUS" :value="detailData.healthStatus" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('visitor.record.securityCheckResult')">
            <dict-tag
              :type="DICT_TYPE.VISITOR_SECURITY_CHECK_RESULT"
              :value="detailData.securityCheckResult"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="detailData.vehiclePhoto">
        <el-col :span="24">
          <el-form-item :label="t('visitor.record.vehiclePhoto')">
            <el-image
              :src="detailData.vehiclePhoto"
              style="width: 200px; height: 150px"
              :preview-src-list="[detailData.vehiclePhoto]"
              fit="cover"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="detailData.visitorPhoto">
        <el-col :span="24">
          <el-form-item :label="t('visitor.record.visitorPhoto')">
            <el-image
              :src="detailData.visitorPhoto"
              style="width: 200px; height: 150px"
              :preview-src-list="[detailData.visitorPhoto]"
              fit="cover"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="detailData.abnormalInfo">
        <el-col :span="24">
          <el-form-item :label="t('visitor.record.abnormalInfo')">
            <span>{{ detailData.abnormalInfo }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="detailData.abnormalPhotos && detailData.abnormalPhotos.length > 0">
        <el-col :span="24">
          <el-form-item :label="t('visitor.record.abnormalPhotos')">
            <el-image
              v-for="(photo, index) in detailData.abnormalPhotos"
              :key="index"
              :src="photo"
              style="width: 100px; height: 80px; margin-right: 10px"
              :preview-src-list="detailData.abnormalPhotos"
              fit="cover"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="detailData.remarks">
        <el-col :span="24">
          <el-form-item :label="t('visitor.record.remarks')">
            <span>{{ detailData.remarks }}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="detailVisible = false">{{ t('common.close') }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { dateFormatter, formatDate } from '@/utils/formatTime'
import { DICT_TYPE } from '@/utils/dict'
import * as VisitorRecordApi from '@/api/visitor/record'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  applicationId: undefined,
  applicationNo: undefined,
  visitorName: undefined,
  visitorPhone: undefined,
  operationType: undefined,
  operatorId: undefined,
  operatorName: undefined,
  gateLocation: undefined,
  verificationMethod: undefined,
  vehiclePlate: undefined,
  healthStatus: undefined,
  securityCheckResult: undefined,
  operationTime: [],
  createTime: []
})
const queryFormRef = ref() // 搜索的表单

const detailVisible = ref(false) // 详情弹窗的是否展示
const detailData = ref() // 详情的数据

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await VisitorRecordApi.getVisitorRecordPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 查看详情 */
const openDetail = async (row: VisitorRecordApi.VisitorRecordVO) => {
  detailData.value = row
  detailVisible.value = true
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
